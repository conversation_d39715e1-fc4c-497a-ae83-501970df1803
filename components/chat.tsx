'use client';

import type { Attachment, UIMessage } from 'ai';
import { useChat } from '@ai-sdk/react';
import { useState } from 'react';
import { useSWRConfig } from 'swr';
import { ChatHeader } from '@/components/chat-header';
import { generateUUID } from '@/lib/utils';
import { Artifact } from './artifact';
import { MultimodalInput } from './multimodal-input';
import { Messages } from './messages';
import { VisibilityType } from './visibility-selector';
import { useArtifactSelector } from '@/hooks/use-artifact';
import { toast } from 'sonner';

export function Chat({
  id,
  initialMessages,
  selectedChatModel,
  selectedVisibilityType,
  isReadonly,
}: {
  id: string;
  initialMessages: Array<UIMessage>;
  selectedChatModel: string;
  selectedVisibilityType: VisibilityType;
  isReadonly: boolean;
}) {
  const { mutate } = useSWRConfig();

  const {
    messages,
    setMessages,
    handleSubmit,
    input,
    setInput,
    append,
    status,
    stop,
    reload,
    addToolResult,
    data,
  } = useChat({
    id,
    body: { id, selectedChatModel: selectedChatModel },
    initialMessages,
    maxSteps: 10,
    experimental_throttle: 100,
    sendExtraMessageFields: true,
    generateId: generateUUID,
    onFinish: () => {
      mutate('/api/history');

      // Add ask-for-confirmation part if the triggering user message had attachments
      setMessages((currentMessages) => {
        // Find the user message that triggered this assistant response
        let triggeringUserMessage = null;

        // Look backwards from the current assistant message to find the most recent user message
        for (let i = currentMessages.length - 1; i >= 0; i--) {
          if (currentMessages[i].role === 'user') {
            triggeringUserMessage = currentMessages[i];
            break;
          }
        }

        // Check if the user message had attachments
        if (
          triggeringUserMessage?.experimental_attachments &&
          triggeringUserMessage.experimental_attachments.length > 0
        ) {
          // Find the assistant message to modify (should be the last one)
          const updatedMessages = [...currentMessages];
          const lastMessageIndex = updatedMessages.length - 1;
          const lastMessage = updatedMessages[lastMessageIndex];

          if (lastMessage && lastMessage.role === 'assistant') {
            const currentParts = lastMessage.parts || [];

            // Check if ask-for-confirmation part already exists
            const hasConfirmationPart = currentParts.some(
              (part) =>
                part.type === 'tool-invocation' &&
                part.toolInvocation?.toolName === 'ask-for-confirmation',
            );

            if (!hasConfirmationPart) {
              // Add the ask-for-confirmation part
              const newPart = {
                type: 'tool-invocation' as const,
                toolInvocation: {
                  toolName: 'ask-for-confirmation',
                  toolCallId: generateUUID(),
                  state: 'call' as const,
                  args: JSON.stringify({
                    messageId: lastMessage.id,
                  }),
                },
              };

              updatedMessages[lastMessageIndex] = {
                ...lastMessage,
                parts: [...currentParts, newPart],
              };

              return updatedMessages;
            }
          }
        }

        return currentMessages;
      });
    },
    onError: () => {
      toast.error('An error occured, please try again!');
    },
    // run client-side tools that are automatically executed:
    async onToolCall() {
      console.log('tool called');
    },
  });

  const [attachments, setAttachments] = useState<Array<Attachment>>([]);
  const isArtifactVisible = useArtifactSelector((state) => state.isVisible);

  return (
    <>
      <div className="flex flex-col min-w-0 h-dvh bg-background">
        <ChatHeader
          chatId={id}
          selectedModelId={selectedChatModel}
          selectedVisibilityType={selectedVisibilityType}
          isReadonly={isReadonly}
        />

        <Messages
          chatId={id}
          status={status}
          votes={[]}
          messages={messages}
          setMessages={setMessages}
          reload={reload}
          isReadonly={isReadonly}
          isArtifactVisible={isArtifactVisible}
          addToolResult={addToolResult}
          data={data}
          append={append}
        />

        <form className="flex mx-auto px-4 bg-background pb-4 md:pb-6 gap-2 w-full md:max-w-3xl">
          {!isReadonly && (
            <MultimodalInput
              chatId={id}
              input={input}
              setInput={setInput}
              handleSubmit={handleSubmit}
              status={status}
              stop={stop}
              attachments={attachments}
              setAttachments={setAttachments}
              messages={messages}
              setMessages={setMessages}
              append={append}
            />
          )}
        </form>
        <p className="text-xs text-muted-foreground mt-0 mb-2 text-center">
          George can make mistakes. Check important info.
        </p>
      </div>

      <Artifact
        chatId={id}
        input={input}
        setInput={setInput}
        handleSubmit={handleSubmit}
        status={status}
        stop={stop}
        attachments={attachments}
        setAttachments={setAttachments}
        append={append}
        messages={messages}
        setMessages={setMessages}
        reload={reload}
        votes={[]}
        isReadonly={isReadonly}
      />
    </>
  );
}
